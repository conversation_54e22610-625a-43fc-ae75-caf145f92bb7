/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useRef, useState, useEffect, Suspense } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import "./chat.css";
import { useTheme } from "@/contexts/ThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import { Input } from "@/components/ui/input";
import { Conversation, Dish } from "@/app/type";
import { formatTime } from "@/app/helper/time";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react/dist/iconify.js";
import {
  ChevronLeft,
  Minus,
  Plus,
  ShoppingBag,
  ShoppingCart,
  MapPin,
  Trash2,
  LogOut,
  Clock,
  X,
} from "lucide-react";
import {
  botName,
  firstLetterExtractor,
  formatStringToHtml,
  generateChatPlaceHolder,
  generateInitialMessages,
} from "@/app/helper/chat";
import {
  clearConversation,
  conversation,
  getConversation,
  getOutletMenu,
} from "@/server/user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import useBackendCart from "@/hooks/useBackendCart";
import { arrayOperations, stringReducer } from "@/app/helper/general";
import OffersCarousel from "@/components/custom/offers/OffersCarousel";
import { Offer } from "@/app/type";
import { calculateTotalWithOffers, CartItem } from "@/utils/offerCalculations";
import { getApplicableOffers } from "@/server/marketing";
import MicRecorder from "@/components/custom/users/MicRecorder";
import TextToSpeech from "@/components/custom/users/TextToSpeech";
import { useLanguagePreference } from "../../../../hooks/useLanguagePreference";
import LanguageSelector from "../../../../components/LanguageSelector";

const Page = () => {
  const {
    cart,
    appliedOffers,
    cartTotals,
    loading: cartLoading,
    addToCart: addToCartBackend,
    removeFromCart: removeFromCartBackend,
    updateItemQuantity,
    getCartItemCount,
    isItemInCart,
    getItemQuantity,
  } = useBackendCart();
  const router = useRouter();
  const {
    language,
    setLanguage,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isLoading: languageLoading,
  } = useLanguagePreference();
  const params = useSearchParams();
  const outletId = params?.get("outletId") || "";
  const [isCartMenuOpen, setIsCartMenuOpen] = useState(false);
  const foodChainId = params?.get("chainId") || "";
  const [isOpen, setIsOpen] = useState(false);
  const [userMessage, setUserMessage] = useState("");
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isMenuClicked, setIsMenuClicked] = useState(false);
  const { theme, isLoading, setChainAndOutlet } = useTheme();
  const { logout } = useAuth();
  const [responseLoading, setResponseLoading] = useState(false);
  const [placeHolder, setPlaceholder] = useState(generateChatPlaceHolder());
  const [conversationHistory, setConversationHistory] = useState<
    Conversation[]
  >([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [menu, setMenu] = useState<{ category: string; dishes: Dish[] }[]>([]);

  // Reset animation after some time of inactivity
  useEffect(() => {
    let timer: any;
    if (isMenuClicked) {
      timer = setTimeout(() => {
        setIsMenuClicked(false);
      }, 60000); // Reset after 1 minute of inactivity
    }
    return () => clearTimeout(timer);
  }, [isMenuClicked]);

  // Handle category selection
  const handleCategorySelect = (category: any) => {
    setSelectedCategory(category);
  };

  const handleAddToCart = async (item: Dish) => {
    if (isItemInCart(item._id!)) {
      // If item is already in cart, remove it
      await removeFromCartBackend(item._id!);
    } else {
      // Add item to cart
      await addToCartBackend(item, 1);
    }
  };

  const handleUpdateQuantity = async (
    dish: Dish,
    action: "increase" | "decrease"
  ) => {
    const currentQuantity = getItemQuantity(dish._id!);

    if (action === "increase") {
      await updateItemQuantity(dish._id!, currentQuantity + 1);
    } else if (action === "decrease") {
      if (currentQuantity === 1) {
        await removeFromCartBackend(dish._id!);
      } else {
        await updateItemQuantity(dish._id!, currentQuantity - 1);
      }
    }
  };

  useEffect(() => {
    if (foodChainId) {
      setChainAndOutlet(foodChainId);
    }
  }, [foodChainId]);

  // Note: Auto-apply offers is now handled by the backend cart system

  const getOutletMenuFunc = async () => {
    if (!foodChainId || !outletId) return;
    const response = await getOutletMenu(String(foodChainId), String(outletId));
    const set = new Set(response.data.map((item: any) => item.category.name));

    // Add categories message
    setConversationHistory([
      {
        sender: botName.lowerCase,
        message: generateInitialMessages(),
        time: new Date().getTime(),
      },
      {
        sender: "user",
        message: "Can you please show me the menu?",
        time: new Date().getTime(),
      },
      {
        sender: botName.lowerCase,
        message: `We are serving the following categories: ${[...set].join(
          ", "
        )}`,
        time: new Date().getTime(),
        dishes: response.data,
        suggestedQuestions: [
          response.data[0]?.name ? `What should I try in ${[...set][0]}?` : "",
          response.data[0]?.name
            ? `Add ${response.data[0]?.name} to my order`
            : "",
        ],
      },
    ]);

    // Use saved recommended dishes if available, otherwise use all dishes
    const dishesToShow = response.data;
    updateConversationWithGroupedDishes(dishesToShow, [
      response.data[0]?.name ? `What should I try in ${[...set][0]}?` : "",
      response.data[0]?.name ? `Add ${response.data[0]?.name} to my order` : "",
    ]);

    setMenu(groupDishesByCategory(response.data) as any);

    // Get previous conversations
    const conversationData = await getConversation(String(outletId));
    if (conversationData?.data?.messages) {
      setConversationHistory((prev) => [
        ...prev,
        ...conversationData.data.messages,
      ]);

      // Scroll to bottom after loading conversation history
      setTimeout(() => scrollToBottom(true), 300);
    }
  };

  useEffect(() => {
    // Then fetch menu and previous conversations
    setTimeout(() => {
      getOutletMenuFunc();
    }, 100);
  }, []);

  const scrollToBottom = (force = false) => {
    if (chatContainerRef.current) {
      // Find the scrollable container - it should be the one with overflow-y-auto
      const scrollContainer =
        chatContainerRef.current.querySelector(".overflow-y-auto");

      if (scrollContainer) {
        const scrollToBottomAction = () => {
          const isAtBottom =
            scrollContainer.scrollTop + scrollContainer.clientHeight >=
            scrollContainer.scrollHeight - 10;

          // Always scroll if forced, or if user is already near the bottom
          if (force || isAtBottom || scrollContainer.scrollTop === 0) {
            scrollContainer.scrollTo({
              top: scrollContainer.scrollHeight,
              behavior: "smooth",
            });
          }
        };

        // Use requestAnimationFrame for better performance
        requestAnimationFrame(() => {
          setTimeout(scrollToBottomAction, 50);
        });
      } else {
        // Fallback: scroll the chat container itself
        requestAnimationFrame(() => {
          setTimeout(() => {
            if (chatContainerRef.current) {
              chatContainerRef.current.scrollTo({
                top: chatContainerRef.current.scrollHeight,
                behavior: "smooth",
              });
            }
          }, 50);
        });
      }
    }
  };

  const handleClearConversation = () => {
    clearConversation(String(outletId)).then(() => {
      router.push(`/conversations`);
    });
    setConversationHistory([]);
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversationHistory, responseLoading]);

  // Additional scroll effect for when new messages are added
  useEffect(() => {
    if (conversationHistory.length > 0) {
      // Scroll to bottom when a new message is added
      const timer = setTimeout(() => {
        scrollToBottom(true);
      }, 200); // Slightly longer delay to ensure content is rendered

      return () => clearTimeout(timer);
    }
  }, [conversationHistory.length]);

  // Scroll to bottom on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      scrollToBottom(true);
    }, 500); // Give time for initial render

    return () => clearTimeout(timer);
  }, []);

  const handleNewMessage = (message: string) => {
    if (responseLoading) return;

    setResponseLoading(true);

    // Add user's message to conversation history
    setConversationHistory((prev) => [
      ...prev,
      { sender: "user", message, time: new Date().getTime() },
    ]);

    // Scroll to bottom after adding user message (force scroll)
    setTimeout(() => scrollToBottom(true), 100);

    let fullText = "";

    // Stream data from the backend
    const days = 1;
    const dayAgo = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    const recentConversation = conversationHistory
      .slice(3)
      .slice(-6) // Get the last 6 entries
      .filter((item) => item?.time && new Date(item?.time) >= dayAgo);

    conversation(
      String(foodChainId),
      userMessage || message,
      (text: string) => {
        fullText += text;
        setConversationHistory((prev) => {
          // Update the butler's latest message or create a new one
          const lastMessage = prev[prev.length - 1];
          if (lastMessage?.sender === botName.lowerCase) {
            return [
              ...prev.slice(0, -1),
              { ...lastMessage, message: fullText },
            ];
          }
          return [
            ...prev,
            {
              sender: botName.lowerCase,
              message: fullText,
              time: new Date().getTime(),
            },
          ];
        });

        // Scroll to bottom during streaming to follow the text
        setTimeout(() => scrollToBottom(), 50);
      },
      (dishes: Dish[], faqSuggestions: string[]) => {
        // Always update conversation, even with empty dishes
        if (faqSuggestions?.length > 0) {
          setPlaceholder(faqSuggestions[0]);
        }
        // Pass dishes (even if empty) to the update function
        updateConversationWithGroupedDishes(dishes || [], faqSuggestions || []);
      },
      () => {
        setResponseLoading(false);
        setUserMessage(""); // Clear the user message input

        // Final scroll to bottom when response is complete (force scroll)
        setTimeout(() => scrollToBottom(true), 200);
      },
      String(outletId),
      recentConversation || [],
      language // Pass the selected language
    );
  };

  // Function to group dishes by category
  const groupDishesByCategory = (dishes: Dish[]) => {
    // Create an object to hold grouped dishes
    let isFeatured = false;
    const groupedDishes: any = {
      Featured: [],
    };

    // Group dishes by their category
    dishes.forEach((dish: any) => {
      const category = dish?.category?.name || "-";
      if (!groupedDishes[category]) {
        groupedDishes[category] = [];
      }
      groupedDishes[category].push(dish);
      if (dish?.isFeatured) {
        isFeatured = true;
        groupedDishes.Featured.push(dish);
      }
    });
    if (!isFeatured) {
      delete groupedDishes.Featured;
    }

    // Convert to array format if needed
    return Object.entries(groupedDishes).map(([category, dishes]) => ({
      category,
      dishes,
    }));
  };

  // Update the conversation history with grouped dishes
  const updateConversationWithGroupedDishes = (
    dishes: Dish[],
    faqSuggestions: string[],
    cartOperationResult?: any
  ) => {
    // Handle empty dishes array gracefully
    const dishesArray = Array.isArray(dishes) ? dishes : [];
    const groupedDishes =
      dishesArray.length > 0 ? groupDishesByCategory(dishesArray) : [];

    setConversationHistory((prev: any) => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage?.sender === botName.lowerCase) {
        return [
          ...prev.slice(0, -1),
          {
            ...lastMessage,
            groupedDishes: groupedDishes,
            dishes: dishesArray,
            suggestedQuestions: faqSuggestions || [],
            hasDishes: dishesArray.length > 0,
            cartOperation: cartOperationResult, // Add cart operation result
          },
        ];
      }
      return prev;
    });

    // If there was a cart operation, highlight the dish but don't modify the cart
    // The user will need to click the + button to actually add it
    if (cartOperationResult?.success) {
      // Instead of automatically adding/removing from cart,
      // we'll just make sure the dish is in the recommendations
      // The user can then add it manually using the + button
      // We could optionally highlight the dish in the UI to draw attention to it
      // This could be done by adding a temporary class or state
      // For now, we'll just ensure the dish is visible in the recommendations
      // The actual cart modification will be done by the user clicking the + button
    }

    // Scroll to bottom after updating conversation with dishes (force scroll)
    setTimeout(() => scrollToBottom(true), 150);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const input = e.currentTarget;
      if (input.value.trim()) {
        handleNewMessage(input.value);
        input.value = "";
      }
    }
  };

  const handleInputFocus = () => {
    // Handle keyboard overlay on mobile
    if (window.innerWidth <= 768) {
      // Small delay to allow keyboard to start opening
      setTimeout(() => {
        // Scroll to ensure input is visible
        const inputContainer = document.querySelector(".input-container");
        if (inputContainer) {
          inputContainer.scrollIntoView({
            behavior: "smooth",
            block: "end",
            inline: "nearest",
          });
        }
      }, 150);
    }
  };

  useEffect(() => {
    // Add chat page body class
    document.body.classList.add("chat-page-body");

    const setAppHeight = () => {
      const doc = document.documentElement;
      const windowHeight = window.innerHeight;
      const visualViewportHeight =
        window.visualViewport?.height || windowHeight;

      // Use the smaller of the two heights to handle keyboard overlay
      const effectiveHeight = Math.min(windowHeight, visualViewportHeight);

      const vh = effectiveHeight * 0.01;
      doc.style.setProperty("--vh", `${vh}px`);
      doc.style.setProperty("--app-height", `${effectiveHeight}px`);
    };

    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        const windowHeight = window.innerHeight;
        const visualViewportHeight = window.visualViewport.height;

        // Use visual viewport height when keyboard is open
        const effectiveHeight = Math.min(windowHeight, visualViewportHeight);
        const vh = effectiveHeight * 0.01;

        document.documentElement.style.setProperty("--vh", `${vh}px`);
        document.documentElement.style.setProperty(
          "--app-height",
          `${effectiveHeight}px`
        );
      }
    };

    // Set initial height
    setAppHeight();

    // Listen for resize events
    window.addEventListener("resize", setAppHeight);

    // Listen for visual viewport changes (keyboard open/close)
    if (window.visualViewport) {
      window.visualViewport.addEventListener(
        "resize",
        handleVisualViewportChange
      );
    }

    return () => {
      window.removeEventListener("resize", setAppHeight);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleVisualViewportChange
        );
      }
      // Remove chat page body class on cleanup
      document.body.classList.remove("chat-page-body");
    };
  }, []);

  // Enhanced keyboard detection and handling
  useEffect(() => {
    let isKeyboardOpen = false;

    const handleKeyboardToggle = () => {
      if (window.innerWidth <= 768) {
        const currentHeight =
          window.visualViewport?.height || window.innerHeight;
        const windowHeight = window.innerHeight;
        const keyboardHeight = windowHeight - currentHeight;
        const wasKeyboardOpen = isKeyboardOpen;

        // More reliable keyboard detection
        isKeyboardOpen = keyboardHeight > 150;

        if (isKeyboardOpen !== wasKeyboardOpen) {
          const mainContainer = document.querySelector(".main-container");

          if (isKeyboardOpen) {
            document.body.classList.add("keyboard-open");
            if (mainContainer) {
              mainContainer.classList.add("keyboard-open");
            }
            // Prevent background scrolling
            document.body.style.overflow = "hidden";
            document.body.style.position = "fixed";
            document.body.style.width = "100%";
          } else {
            document.body.classList.remove("keyboard-open");
            if (mainContainer) {
              mainContainer.classList.remove("keyboard-open");
            }
            // Restore scrolling
            document.body.style.overflow = "";
            document.body.style.position = "";
            document.body.style.width = "";
          }
        }
      }
    };

    // Listen for both resize and visual viewport changes
    window.addEventListener("resize", handleKeyboardToggle);
    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", handleKeyboardToggle);
    }

    return () => {
      window.removeEventListener("resize", handleKeyboardToggle);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleKeyboardToggle
        );
      }
      // Cleanup
      document.body.classList.remove("keyboard-open");
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, []);

  // Add viewport meta tag for better mobile handling
  useEffect(() => {
    const metaViewport = document.querySelector('meta[name="viewport"]');
    if (metaViewport) {
      metaViewport.setAttribute(
        "content",
        "width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover"
      );
    }

    return () => {
      // Restore original viewport on cleanup
      if (metaViewport) {
        metaViewport.setAttribute(
          "content",
          "width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover"
        );
      }
    };
  }, []);

  const handleButtonClick = () => {
    const input = document.querySelector("input");
    if (input && input.value.trim()) {
      handleNewMessage(input.value);
      input.value = "";
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col main-container chat-page" // Added chat-page class
      style={{
        backgroundColor: theme.primaryColor + "20",
        height: "100vh",
        minHeight: "100vh",
        maxHeight: "100vh",
      }}
    >
      <header className="sticky top-0 z-50 bg-white border-b shadow-sm flex-shrink-0 chat-header">
        <div className="max-w-screen-xl mx-auto px-2 py-2 md:px-4 md:py-3 flex items-center justify-between w-full min-h-[60px]">
          <div className="flex items-center gap-1 md:gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full h-10 w-10 md:h-8 md:w-8 mobile-touch-target"
              onClick={() => {
                router.push(
                  localStorage.getItem("to-outlets")
                    ? "/outlets"
                    : `/conversations`
                );
                localStorage.removeItem("to-outlets");
              }}
              aria-label="Back to conversations"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>

            <div id="amoeba"></div>

            <h1 className="font-semibold  text-md md:text-lg">
              {firstLetterExtractor(theme?.name).toUpperCase()}&apos;s{" "}
              {botName?.upperCase}
            </h1>
          </div>

          <div className="flex items-center gap-2">
            {/* Language Selector */}
            <LanguageSelector
              selectedLanguage={language}
              onLanguageChange={setLanguage}
              className="mr-2"
            />

            <DropdownMenu
              open={isCartMenuOpen}
              onOpenChange={(open) => {
                setIsCartMenuOpen(open);
                if (open) setIsMenuClicked(true);
              }}
            >
              <DropdownMenuTrigger asChild>
                <div className="relative">
                  <Button
                    variant={"ghost"}
                    className="rounded-full shadow-md cursor-pointer h-10 w-10 md:h-8 md:w-8 mobile-touch-target"
                    style={{ background: theme.primaryColor, color: "white" }}
                    // onClick={() => router.push("/checkout")}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                  {cart.length != 0 && (
                    <div className="bg-red-500 h-6 w-6 flex justify-center text-white text-xs p-1 rounded-full absolute -top-2 -right-2">
                      {cart.length}
                    </div>
                  )}
                </div>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                className="w-[95vw] max-w-96 p-0 rounded-lg border-0 shadow-lg mr-2 md:mr-6"
                sideOffset={5}
                align="end"
              >
                <div className="flex flex-col">
                  <div
                    className="bg-gradient-to-r p-4 rounded-t-lg"
                    style={{
                      backgroundImage: `linear-gradient(to right, ${theme.primaryColor}, ${theme.primaryColor}99)`,
                    }}
                  >
                    <DropdownMenuLabel className="text-white text-md md:text-lg font-bold flex items-center gap-2">
                      <ShoppingCart size={20} />
                      Your Order ({cart.length})
                    </DropdownMenuLabel>
                    <p className="text-white text-xs md:text-sm opacity-90">
                      {(() => {
                        const cartItems: CartItem[] = cart.map((item) => ({
                          ...item,
                          quantity: item.quantity || 1,
                        }));
                        const subtotal = cartItems.reduce(
                          (sum, item) => sum + item.price * item.quantity,
                          0
                        );

                        if (appliedOffers.length > 0) {
                          const calculation = calculateTotalWithOffers(
                            cartItems,
                            appliedOffers
                          );
                          return (
                            <span>
                              <span className="line-through opacity-70">
                                ₹{subtotal}
                              </span>
                              <span className="ml-2 font-bold">
                                ₹{calculation.discountedTotal}
                              </span>
                              <span className="block text-xs text-green-200">
                                {appliedOffers.length} offer
                                {appliedOffers.length > 1 ? "s" : ""} applied
                              </span>
                            </span>
                          );
                        }

                        return `Total: ₹${subtotal}`;
                      })()}
                    </p>
                  </div>

                  <ScrollArea className="max-h-[40vh] md:max-h-[60vh] overflow-y-auto">
                    {cart.length === 0 ? (
                      <div className="p-8 text-center text-gray-500">
                        <ShoppingBag
                          size={40}
                          className="mx-auto mb-3 opacity-50"
                        />
                        <p>Your cart is empty</p>
                        <p className="text-xs md:text-sm mt-1">
                          Add items to get started
                        </p>
                      </div>
                    ) : (
                      cart.map((dish, dishIndex) => (
                        <div key={dishIndex} className="relative">
                          <div className="flex justify-between p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex-1 pr-16">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{dish.name}</span>
                                {dish.isVeg && (
                                  <div className="h-4 w-4 border border-green-600 flex items-center justify-center">
                                    <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                                  </div>
                                )}
                              </div>

                              {dish.description && (
                                <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                                  {stringReducer(dish.description, 20)}
                                </div>
                              )}

                              <div className="flex items-center mt-2">
                                <Badge
                                  variant="outline"
                                  className="font-bold"
                                  style={{
                                    color: theme.primaryColor,
                                    borderColor: theme.primaryColor,
                                  }}
                                >
                                  ₹{dish.price * (dish.quantity || 1)}
                                </Badge>
                              </div>
                            </div>

                            <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-1">
                              <Button
                                onClick={() =>
                                  handleUpdateQuantity(dish, "decrease")
                                }
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 rounded-full bg-gray-100 hover:bg-gray-200"
                                style={{ color: theme.primaryColor }}
                              >
                                <Minus size={16} />
                              </Button>

                              <span className="w-6 text-center font-medium">
                                {dish.quantity || 1}
                              </span>

                              <Button
                                onClick={() =>
                                  handleUpdateQuantity(dish, "increase")
                                }
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 rounded-full bg-gray-100 hover:bg-gray-200"
                                style={{ color: theme.primaryColor }}
                              >
                                <Plus size={16} />
                              </Button>
                            </div>
                          </div>
                          {dishIndex < cart.length - 1 && (
                            <Separator className="my-0 mx-4" />
                          )}
                        </div>
                      ))
                    )}
                  </ScrollArea>

                  <div className="p-4 border-t bg-gray-50 rounded-b-lg">
                    <div className="flex justify-between mb-3 font-medium">
                      <span>Subtotal</span>
                      <span>
                        {(() => {
                          const cartItems: CartItem[] = cart.map((item) => ({
                            ...item,
                            quantity: item.quantity || 1,
                          }));
                          const subtotal = cartItems.reduce(
                            (sum, item) => sum + item.price * item.quantity,
                            0
                          );

                          if (appliedOffers.length > 0) {
                            const calculation = calculateTotalWithOffers(
                              cartItems,
                              appliedOffers
                            );
                            return (
                              <span>
                                <span className="line-through opacity-70">
                                  ₹{subtotal}
                                </span>
                                <span className="ml-2 font-bold">
                                  ₹{calculation.discountedTotal}
                                </span>
                              </span>
                            );
                          }

                          return `₹${subtotal}`;
                        })()}
                      </span>
                    </div>

                    {/* Show applied offers breakdown */}
                    {appliedOffers.length > 0 &&
                      (() => {
                        const cartItems: CartItem[] = cart.map((item) => ({
                          ...item,
                          quantity: item.quantity || 1,
                        }));
                        const calculation = calculateTotalWithOffers(
                          cartItems,
                          appliedOffers
                        );

                        return (
                          <div className="mb-3 space-y-1">
                            {calculation.appliedOffers.map((offer, index) => (
                              <div
                                key={index}
                                className="flex justify-between text-xs md:text-sm text-green-600"
                              >
                                <span>{offer.description}</span>
                                <span>-₹{offer.discount.toFixed(2)}</span>
                              </div>
                            ))}
                          </div>
                        );
                      })()}

                    <Button
                      className="w-full py-6 rounded-md font-medium text-white text-xs md:text-sm transition-colors flex items-center justify-center gap-2"
                      style={{ backgroundColor: theme.primaryColor }}
                      onClick={() =>
                        router.push(`/checkout${window.location.search}`)
                      }
                      disabled={cart.length === 0}
                    >
                      <ShoppingBag size={18} />
                      Proceed to Checkout
                    </Button>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 md:h-8 md:w-8 rounded-full mobile-touch-target"
                >
                  <Icon
                    icon="simple-line-icons:options-vertical"
                    width="20"
                    height="20"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-36">
                <DropdownMenuLabel>Settings</DropdownMenuLabel>
                <Separator />
                <DropdownMenuItem onClick={() => handleClearConversation()}>
                  <span className="flex items-center justify-between w-full">
                    Clear chat <Trash2 className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push("/outlets")}>
                  <span className="flex items-center justify-between w-full">
                    Outlets <MapPin className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    localStorage.setItem("to-chat", "true");
                    router.push("/orders");
                  }}
                >
                  <span className="flex items-center justify-between w-full">
                    Orders <Clock className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => logout()}>
                  <span className="flex items-center justify-between w-full">
                    Logout <LogOut className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div
        className="flex-1 overflow-hidden px-2 min-h-0 chat-area"
        ref={chatContainerRef}
      >
        <div className="bg-white border h-full rounded-2xl p-2 md:p-3 flex flex-col gap-1 md:gap-4 overflow-y-auto">
          {conversationHistory.map((conversation, index) => {
            const isUserSender = conversation.sender === "user";
            const currentTime = conversation.time
              ? new Date(conversation.time).getTime()
              : null;
            const previousTime =
              index > 0 && conversationHistory[index - 1]?.time
                ? new Date(conversationHistory[index - 1].time!).getTime()
                : null;
            // Calculate time difference in minutes
            const timeDifference =
              currentTime && previousTime
                ? (currentTime - previousTime) / (1000 * 60)
                : 0;

            // Show separator if time difference is more than 30 minutes (you can adjust this)
            const showSeparator = index > 0 && timeDifference > 60;

            // Helper function to format the separator date
            const formatSeparatorDate = (timestamp: number) => {
              const date = new Date(timestamp);
              const today = new Date();
              const yesterday = new Date(today);
              yesterday.setDate(yesterday.getDate() - 1);

              if (date.toDateString() === today.toDateString()) {
                return "Recent";
              } else if (date.toDateString() === yesterday.toDateString()) {
                return "Yesterday";
              } else {
                return date.toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                });
              }
            };
            return (
              <div key={index}>
                {/* Time Separator */}
                {showSeparator && (
                  <div className="flex items-center justify-center my-6">
                    <div className="flex-grow h-px bg-gray-200"></div>
                    <div className="px-4 py-2 bg-gray-100 rounded-full text-xs text-gray-500 font-medium">
                      {currentTime && formatSeparatorDate(currentTime)}
                    </div>
                    <div className="flex-grow h-px bg-gray-200"></div>
                  </div>
                )}

                <div
                  className={`flex items-end fle relative ${
                    isUserSender ? "justify-end" : "justify-start"
                  } ${index == 0 ? `mt-4` : `mt-2`}`}
                >
                  <div
                    className={`relative max-w-[85%] md:max-w-4/5 p-2 md:p-3 rounded-lg shadow-md text-xs md:text-sm min-w-[50px] ${
                      !isUserSender
                        ? ` text-white rounded-bl-none`
                        : " text-black rounded-br-none"
                    }`}
                    style={{
                      background: isUserSender ? "#d0d0d0" : theme.primaryColor,
                    }}
                    id="streamingText"
                  >
                    <div
                      className={`text-xs text-gray-400 absolute top-[-1rem] ${
                        isUserSender ? "right-0" : "left-0"
                      }`}
                    >
                      {conversation.time
                        ? formatTime(new Date(conversation.time).getTime())
                        : ""}
                    </div>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: formatStringToHtml(conversation.message),
                      }}
                    ></div>
                    {!isUserSender && (
                      <TextToSpeech
                        text={conversation.message}
                        options={{
                          voice: "Google US English",
                        }}
                      />
                    )}
                  </div>
                </div>

                {/* Display FAQ suggestions if available */}
                {!isUserSender &&
                  conversation.suggestedQuestions &&
                  conversation.suggestedQuestions.length > 0 && (
                    <div className="mt-4 mb-4 ml-4">
                      <div className="flex items-center text-xs md:text-sm text-gray-600 mb-2">
                        <Icon
                          icon="carbon:help"
                          className="mr-2"
                          width="16"
                          height="16"
                        />
                        <span className="font-medium">Suggested Questions</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {conversation.suggestedQuestions.map(
                          (question, qIndex) => (
                            <div
                              key={qIndex}
                              className="cursor-pointer px-3 py-2 bg-gray-50 border border-gray-200 rounded-full text-xs md:text-sm hover:bg-blue-50 hover:border-blue-200 transition-all"
                              style={{ color: theme.secondaryColor }}
                              onClick={() => {
                                setUserMessage(question);
                                handleNewMessage(question);
                              }}
                            >
                              {question}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                {conversation.dishes && conversation.dishes.length > 0 && (
                  <div className="mt-4 mb-4 ml-4 w-[90%]">
                    <div className="flex items-center text-xs md:text-sm text-gray-600 mb-2">
                      <Icon
                        icon="carbon:restaurant"
                        className="mr-2"
                        width="16"
                        height="16"
                      />
                      <span className="font-medium">Recommended Dishes</span>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-3 p-2 md:p-4">
                      {conversation.dishes.map((dish, dishIndex) => (
                        <div
                          key={dishIndex}
                          className={`bg-white border relative border-${
                            dish?.isFeatured ? "yellow" : "gray"
                          }-200 rounded-lg shadow-sm hover:shadow-md transition-all overflow-hidden flex flex-col`}
                        >
                          {dish.image && (
                            <div className="h-32 w-full overflow-hidden relative">
                              <Image
                                src={dish.image}
                                alt={dish.name}
                                fill
                                sizes="(max-width: 768px) 100vw, 33vw"
                                className="object-cover"
                                style={{ objectFit: "cover" }}
                              />
                            </div>
                          )}
                          <div className="p-3 flex flex-col flex-grow">
                            <div className="flex justify-between items-start mb-1">
                              <h3
                                className="font-medium text-xs md:text-sm flex items-center gap-1"
                                style={{ color: theme.secondaryColor }}
                              >
                                {dish?.isFeatured && (
                                  <div className="">
                                    <Icon
                                      icon="mdi:star"
                                      width="20"
                                      height="20"
                                      className="text-yellow-500"
                                    />
                                  </div>
                                )}
                                {dish.name}
                              </h3>
                              <div className="flex items-center gap-1">
                                {dish.isVeg !== undefined ? (
                                  dish.isVeg ? (
                                    <div className="h-4 w-4 border border-green-600 flex items-center justify-center flex-shrink-0 ml-1">
                                      <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                                    </div>
                                  ) : (
                                    <div className="h-4 w-4 border border-red-600 flex items-center justify-center flex-shrink-0 ml-1">
                                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                                    </div>
                                  )
                                ) : null}
                              </div>
                            </div>

                            {dish.description && (
                              <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                                {dish.description}
                              </p>
                            )}

                            <div className="mt-auto flex items-center justify-between">
                              <Badge
                                variant="outline"
                                className="font-bold"
                                style={{
                                  color: theme.primaryColor,
                                  borderColor: theme.primaryColor,
                                }}
                              >
                                ₹{dish.price}
                              </Badge>

                              <Button
                                onClick={() => handleAddToCart(dish)}
                                variant="ghost"
                                size="sm"
                                className="h-8 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200 ml-2"
                                style={{ color: theme.primaryColor }}
                              >
                                {cart.some((i) => i._id === dish._id) ? (
                                  <div className="flex items-center">
                                    <Minus size={14} className="mr-1" />
                                    <span className="text-xs">Remove</span>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <Plus size={14} className="mr-1" />
                                    <span className="text-xs">Add</span>
                                  </div>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
          {responseLoading && (
            <div
              style={{
                background: theme.primaryColor + "20",
                borderLeft: `4px solid ${theme.primaryColor}`,
                boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
              }}
              className="text-center p-3 rounded-xl mt-4 flex items-center justify-center"
            >
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  {[0, 1, 2].map((dot) => (
                    <div
                      key={dot}
                      className="h-2 w-2 rounded-full"
                      style={{
                        background: theme.primaryColor,
                        animation: `pulse 1.5s infinite ease-in-out ${
                          dot * 0.2
                        }s`,
                      }}
                    />
                  ))}
                </div>
                <span className="font-medium">Cooking your response...</span>
              </div>
              <style jsx>{`
                @keyframes pulse {
                  0%,
                  100% {
                    transform: scale(1);
                    opacity: 1;
                  }
                  50% {
                    transform: scale(1.2);
                    opacity: 0.7;
                  }
                }
              `}</style>
            </div>
          )}
        </div>
      </div>
      <div className="sticky bottom-0 bg-white border-t shadow-lg input-container">
        <div className="p-3 md:p-4 max-w-4xl mx-auto">
          <div className="relative flex items-center gap-2 md:gap-3">
            {/* Input Field */}
            <div className="flex-1 relative">
              <Input
                className="w-full h-12 md:h-14 pl-4 pr-12 md:pr-14 rounded-2xl border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all duration-200 text-base bg-gray-50 focus:bg-white shadow-sm"
                placeholder={
                  conversationHistory.at(-1)?.suggestedQuestions?.[0] ||
                  placeHolder
                }
                autoFocus={userMessage.trim() === ""}
                onKeyDown={handleKeyPress}
                onFocus={handleInputFocus}
                value={userMessage}
                onChange={(e) => setUserMessage(e.target.value)}
                style={{
                  fontSize: "16px", // Prevents zoom on iOS
                }}
              />

              {/* Clear Button */}
              {userMessage.trim() !== "" && !responseLoading && (
                <button
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                  onClick={() => setUserMessage("")}
                  type="button"
                >
                  <Icon
                    icon="iconoir:cancel"
                    width="18"
                    height="18"
                    className="text-gray-400"
                  />
                </button>
              )}
            </div>

            {/* Voice Input Button */}
            <MicRecorder
              setUserMessage={setUserMessage}
              language={language}
              disabled={responseLoading}
              className="flex-shrink-0"
            />

            {/* Send Button */}
            <Button
              className={`send-button h-12 w-12 md:h-14 md:w-14 rounded-2xl shadow-lg ${
                userMessage.trim() === "" || responseLoading
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
              style={{
                backgroundColor: theme.primaryColor,
                border: `2px solid ${theme.primaryColor}`,
              }}
              onClick={() => handleButtonClick()}
              disabled={userMessage.trim() === "" || responseLoading}
              type="button"
            >
              {responseLoading ? (
                <div className="animate-spin">
                  <Icon
                    icon="eos-icons:loading"
                    width="24"
                    height="24"
                    className="text-white"
                  />
                </div>
              ) : (
                <Icon
                  icon="fluent:send-32-regular"
                  width="24"
                  height="24"
                  className="text-white ml-0.5"
                />
              )}
            </Button>
          </div>
        </div>

        {/* Menu Button */}
        <div
          className={`fixed md:bottom-24 bottom-${
            isOpen ? "0" : "20"
          } right-4 z-50`}
        >
          <DropdownMenu
            open={isOpen}
            onOpenChange={(open) => {
              setIsOpen(open);
              if (open) setIsMenuClicked(true);
            }}
          >
            <DropdownMenuTrigger asChild onClick={() => setIsOpen(!isOpen)}>
              <div
                className={`relative flex justify-center items-center rounded-full h-8 w-8 md:h-10 md:w-10 cursor-pointer shadow-lg transition-all duration-800 hover:shadow-xl ${
                  !isMenuClicked ? "animate-pulse" : ""
                } ${isOpen ? "scale-110" : "scale-100"}`}
                style={{
                  backgroundColor: theme.primaryColor,
                  border: `2px solid ${theme.primaryColor}`,
                  boxShadow: `0 0 10px ${theme.primaryColor}40`,
                }}
              >
                {isOpen ? (
                  <X className="text-white" />
                ) : (
                  <>
                    <Icon
                      icon="ion:restaurant-outline"
                      width="22"
                      height="22"
                      className="text-white"
                    />
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium">
                      {menu.reduce(
                        (acc, category) =>
                          acc +
                          (category.category === "Featured"
                            ? 0
                            : category.dishes.length),
                        0
                      )}
                    </div>
                  </>
                )}
              </div>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="w-[95vw] max-w-[400px] md:w-80 p-0 rounded-lg border-0 shadow-2xl mr-2 md:mr-6"
              sideOffset={5}
              align="end"
            >
              <div className="flex flex-col">
                <div
                  className="bg-gradient-to-r p-1 md:p-4 rounded-t-lg"
                  style={{
                    backgroundImage: `linear-gradient(to right, ${theme.primaryColor}, ${theme.primaryColor}99)`,
                  }}
                >
                  <DropdownMenuLabel className="text-white text-sm md:text-lg font-bold">
                    Menu
                  </DropdownMenuLabel>
                  <p className="text-white text-sm opacity-90">
                    Select items to order
                  </p>
                </div>

                {/* Offers Carousel */}
                <div className="p-2 md:p-4 bg-gray-50">
                  <OffersCarousel
                    outletId={outletId}
                    foodChainId={foodChainId}
                    onOfferClick={(offer) => {
                      console.log("Offer clicked:", offer);
                      // You can add offer application logic here
                    }}
                  />
                </div>

                {/* Category Navigation */}
                <div className="border-b overflow-x-auto whitespace-nowrap p-2">
                  {menu.map((category, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        handleCategorySelect(
                          category.category === selectedCategory
                            ? ""
                            : category.category
                        );
                        document
                          .getElementById(category.category)
                          ?.scrollIntoView({ behavior: "smooth" });
                      }}
                      className={`inline-block mr-1 px-3 py-1 rounded-full text-sm transition-colors ${
                        selectedCategory === category.category
                          ? "bg-gray-600 text-white font-medium"
                          : "bg-gray-100 hover:bg-gray-200"
                      }`}
                    >
                      {category.category}
                    </button>
                  ))}
                </div>

                {/* Menu Items */}
                <ScrollArea className="max-h-[50vh] overflow-y-scroll">
                  {menu.map((category, categoryIndex) => (
                    <div
                      key={categoryIndex}
                      id={`category-${category.category}`}
                      className={`border rounded-md p-2 ${
                        selectedCategory &&
                        selectedCategory !== category.category
                          ? "opacity-60"
                          : ""
                      }`}
                    >
                      <DropdownMenuLabel
                        className="text-base font-bold px-2 py-1 md:px-4 md:py-2 flex justify-between items-center "
                        id={category.category}
                      >
                        {category.category}
                        <span className="text-xs text-gray-500">
                          {category.dishes.length} items
                        </span>
                      </DropdownMenuLabel>
                      <Separator className="my-2" />

                      {category.dishes.map((dish, dishIndex) => (
                        <div key={dishIndex} className="relative">
                          <div className="flex justify-between px-3 py-1 md:px-4 md:py-3 hover:bg-gray-50 cursor-pointer">
                            <div className="flex-1">
                              <div className="font-medium text-sm md:text-md">
                                {dish.name} {dish?.isFeatured && "🌟"}
                              </div>
                              {dish.description && (
                                <div className="text-xs text-gray-500 mt-1 pr-8">
                                  {stringReducer(dish.description, 100)}
                                </div>
                              )}
                              <div className="flex items-center mt-2">
                                <Badge
                                  variant="outline"
                                  className="font-bold mr-2"
                                  style={{
                                    color: theme.primaryColor,
                                    borderColor: theme.primaryColor,
                                  }}
                                >
                                  ₹{dish.price}
                                </Badge>
                              </div>
                            </div>
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddToCart(dish);
                              }}
                              variant={"ghost"}
                              className="absolute right-4 top-1/2 cursor-pointer -translate-y-1/2 h-8 w-8 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200"
                              style={{ color: theme.primaryColor }}
                            >
                              {cart.some((i) => i._id === dish._id) ? (
                                <Minus size={18} />
                              ) : (
                                <Plus size={18} />
                              )}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ))}
                </ScrollArea>

                <div className="p-3 border-t bg-gray-50 rounded-b-lg">
                  <button
                    className="w-full py-2 rounded-md font-medium text-white text-sm transition-colors"
                    style={{ backgroundColor: theme.primaryColor }}
                    onClick={() => {
                      setIsOpen(false);
                      setIsCartMenuOpen(true);
                    }}
                  >
                    View Cart
                  </button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

const ChatPage = () => {
  return (
    <Suspense>
      <Page />
    </Suspense>
  );
};

export default ChatPage;
