/**
 * Cart System Test Script
 * 
 * This script tests the frontend cart system integration with the backend.
 * It tests all cart operations including:
 * - Adding items to cart
 * - Updating item quantities
 * - Removing items from cart
 * - Clearing the cart
 * - Offer application
 * - AI direct cart operations
 * 
 * To run this test:
 * 1. Make sure the server is running
 * 2. Run: node src/tests/cart-system-test.js
 */

// Import required modules
const fetch = require('node-fetch');
const readline = require('readline');

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001/api/v1';
let authToken = '';
let userId = '';
let foodChainId = '';
let outletId = '';
let testDishId = '';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
};

// Create readline interface for interactive testing
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to prompt user
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Login and get auth token
const login = async () => {
  try {
    console.log('🔑 Logging in...');
    const response = await fetch(`${API_BASE_URL}/user/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    const data = await response.json();
    if (data.success) {
      authToken = data.token;
      userId = data.user._id;
      console.log('✅ Login successful');
      return true;
    } else {
      console.error('❌ Login failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Login error:', error);
    return false;
  }
};

// Get food chain and outlet IDs
const getFoodChainAndOutlet = async () => {
  try {
    console.log('🔍 Getting food chain and outlet...');
    const response = await fetch(`${API_BASE_URL}/user/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    if (data.success) {
      foodChainId = data.data.foodChain;
      
      // Get first outlet
      const outletsResponse = await fetch(`${API_BASE_URL}/user/outlets?foodChainId=${foodChainId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
      });
      
      const outletsData = await outletsResponse.json();
      if (outletsData.success && outletsData.data.length > 0) {
        outletId = outletsData.data[0]._id;
        console.log('✅ Got food chain and outlet IDs');
        return true;
      } else {
        console.error('❌ No outlets found for food chain');
        return false;
      }
    } else {
      console.error('❌ Failed to get user profile:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error getting food chain and outlet:', error);
    return false;
  }
};

// Get a test dish
const getTestDish = async () => {
  try {
    console.log('🍔 Getting test dish...');
    const response = await fetch(`${API_BASE_URL}/user/menu?foodChainId=${foodChainId}&outletId=${outletId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    if (data.success && data.data.length > 0) {
      testDishId = data.data[0]._id;
      console.log(`✅ Got test dish: ${data.data[0].name}`);
      return true;
    } else {
      console.error('❌ No dishes found for food chain');
      return false;
    }
  } catch (error) {
    console.error('❌ Error getting test dish:', error);
    return false;
  }
};

// Test getting cart
const testGetCart = async () => {
  try {
    console.log('🛒 Getting cart...');
    const response = await fetch(`${API_BASE_URL}/user/cart?foodChainId=${foodChainId}&outletId=${outletId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    if (data.success) {
      console.log(`✅ Got cart with ${data.data.items.length} items`);
      return true;
    } else {
      console.error('❌ Failed to get cart:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error getting cart:', error);
    return false;
  }
};

// Test adding item to cart
const testAddToCart = async () => {
  try {
    console.log('➕ Adding item to cart...');
    const response = await fetch(`${API_BASE_URL}/user/cart/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        dishId: testDishId,
        quantity: 1,
        foodChainId,
        outletId,
      }),
    });

    const data = await response.json();
    if (data.success) {
      console.log('✅ Item added to cart successfully');
      return true;
    } else {
      console.error('❌ Failed to add item to cart:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error adding item to cart:', error);
    return false;
  }
};

// Test updating item quantity
const testUpdateItemQuantity = async () => {
  try {
    console.log('🔄 Updating item quantity...');
    const response = await fetch(`${API_BASE_URL}/user/cart/update/${testDishId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        quantity: 2,
        foodChainId,
        outletId,
      }),
    });

    const data = await response.json();
    if (data.success) {
      console.log('✅ Item quantity updated successfully');
      return true;
    } else {
      console.error('❌ Failed to update item quantity:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error updating item quantity:', error);
    return false;
  }
};

// Test removing item from cart
const testRemoveFromCart = async () => {
  try {
    console.log('➖ Removing item from cart...');
    const response = await fetch(`${API_BASE_URL}/user/cart/remove/${testDishId}?foodChainId=${foodChainId}&outletId=${outletId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    if (data.success) {
      console.log('✅ Item removed from cart successfully');
      return true;
    } else {
      console.error('❌ Failed to remove item from cart:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error removing item from cart:', error);
    return false;
  }
};

// Test clearing cart
const testClearCart = async () => {
  try {
    console.log('🧹 Clearing cart...');
    const response = await fetch(`${API_BASE_URL}/user/cart/clear?foodChainId=${foodChainId}&outletId=${outletId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    if (data.success) {
      console.log('✅ Cart cleared successfully');
      return true;
    } else {
      console.error('❌ Failed to clear cart:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Error clearing cart:', error);
    return false;
  }
};

// Test AI cart operations
const testAICartOperations = async () => {
  try {
    console.log('🤖 Testing AI cart operations...');
    
    // First, clear the cart
    await testClearCart();
    
    // Send a message to the AI to add an item
    const message = `Add ${testDishId} to my cart`;
    
    const response = await fetch(`${API_BASE_URL}/user/conversation?foodChainId=${foodChainId}&outletId=${outletId}&message=${encodeURIComponent(message)}&userId=${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });
    
    // Check if the item was added to the cart
    const cartResponse = await fetch(`${API_BASE_URL}/user/cart?foodChainId=${foodChainId}&outletId=${outletId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });
    
    const cartData = await cartResponse.json();
    if (cartData.success && cartData.data.items.some(item => item.dishId === testDishId)) {
      console.log('✅ AI successfully added item to cart');
      return true;
    } else {
      console.log('❌ AI failed to add item to cart');
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing AI cart operations:', error);
    return false;
  }
};

// Run all tests
const runTests = async () => {
  try {
    console.log('🧪 Starting cart system tests...');
    
    // Setup
    const loginSuccess = await login();
    if (!loginSuccess) return;
    
    const foodChainSuccess = await getFoodChainAndOutlet();
    if (!foodChainSuccess) return;
    
    const dishSuccess = await getTestDish();
    if (!dishSuccess) return;
    
    // Tests
    await testGetCart();
    await testAddToCart();
    await testUpdateItemQuantity();
    await testRemoveFromCart();
    await testClearCart();
    await testAICartOperations();
    
    console.log('\n✅ All tests completed');
  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    rl.close();
  }
};

// Run the tests
runTests();
