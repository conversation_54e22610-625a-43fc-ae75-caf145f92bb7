import Cart from "../models/Cart.js";
import Dish from "../models/Dish.js";
import { validateAndApplyOffers } from "../services/offer-validation-service.js";

// Get user's cart
export const getCart = async (req, res) => {
  try {
    const { userId } = req.user;
    const { foodChainId, outletId } = req.query;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);

    if (!cart) {
      cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);
    }

    // Populate dish and offer details
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    res.json({
      success: true,
      data: cart,
    });
  } catch (error) {
    console.error("Error fetching cart:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch cart",
      error: error.message,
    });
  }
};

// Add item to cart
export const addToCart = async (req, res) => {
  try {
    const { userId } = req.user;
    const { dishId, quantity = 1, foodChainId, outletId } = req.body;

    if (!dishId || !foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Dish ID, food chain ID, and outlet ID are required",
      });
    }

    // Fetch dish details
    const dish = await Dish.findById(dishId).populate("category", "name");
    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found",
      });
    }

    if (!dish.isAvailable) {
      return res.status(400).json({
        success: false,
        message: "Dish is not available",
      });
    }

    // Get or create cart
    let cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);

    // Add item to cart
    await cart.addItem(
      {
        ...dish.toObject(),
        categoryName: dish.category?.name,
      },
      quantity
    );

    // Auto-apply offers
    await autoApplyOffers(cart);

    // Populate and return updated cart
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    res.json({
      success: true,
      message: "Item added to cart successfully",
      data: cart,
    });
  } catch (error) {
    console.error("Error adding item to cart:", error);
    res.status(500).json({
      success: false,
      message: "Failed to add item to cart",
      error: error.message,
    });
  }
};

// Remove item from cart
export const removeFromCart = async (req, res) => {
  try {
    const { userId } = req.user;
    const { dishId } = req.params;
    const { foodChainId, outletId } = req.query;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    const cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: "Cart not found",
      });
    }

    await cart.removeItem(dishId);

    // Re-apply offers after item removal
    await autoApplyOffers(cart);

    // Populate and return updated cart
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    res.json({
      success: true,
      message: "Item removed from cart successfully",
      data: cart,
    });
  } catch (error) {
    console.error("Error removing item from cart:", error);
    res.status(500).json({
      success: false,
      message: "Failed to remove item from cart",
      error: error.message,
    });
  }
};

// Update item quantity in cart
export const updateCartItemQuantity = async (req, res) => {
  try {
    const { userId } = req.user;
    const { dishId } = req.params;
    const { quantity, foodChainId, outletId } = req.body;

    console.log("Backend - Update cart item request:", {
      userId,
      dishId,
      quantity,
      foodChainId,
      outletId,
      body: req.body,
      headers: req.headers,
    });

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    if (quantity < 0) {
      return res.status(400).json({
        success: false,
        message: "Quantity cannot be negative",
      });
    }

    const cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: "Cart not found",
      });
    }

    console.log("Found cart:", {
      cartId: cart._id,
      itemsCount: cart.items.length,
      items: cart.items.map((item) => ({
        dishId: item.dishId.toString(),
        quantity: item.quantity,
      })),
    });

    await cart.updateItemQuantity(dishId, quantity);
    console.log(
      "After update - cart items:",
      cart.items.map((item) => ({
        dishId: item.dishId.toString(),
        quantity: item.quantity,
      }))
    );

    // Re-apply offers after quantity update
    await autoApplyOffers(cart);

    // Populate and return updated cart
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    console.log("Sending response with updated cart:", {
      cartId: cart._id,
      itemsCount: cart.items.length,
      subtotal: cart.subtotal,
      finalTotal: cart.finalTotal,
    });

    res.json({
      success: true,
      message: "Cart item quantity updated successfully",
      data: cart,
    });
  } catch (error) {
    console.error("Error updating cart item quantity:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update cart item quantity",
      error: error.message,
    });
  }
};

// Clear cart
export const clearCart = async (req, res) => {
  try {
    const { userId } = req.user;
    const { foodChainId, outletId } = req.query;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    const cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: "Cart not found",
      });
    }

    await cart.clearCart();

    res.json({
      success: true,
      message: "Cart cleared successfully",
      data: cart,
    });
  } catch (error) {
    console.error("Error clearing cart:", error);
    res.status(500).json({
      success: false,
      message: "Failed to clear cart",
      error: error.message,
    });
  }
};

// Sync cart from frontend localStorage
export const syncCart = async (req, res) => {
  try {
    const { userId } = req.user;
    const { items, foodChainId, outletId, appliedOffers = [] } = req.body;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    // Get or create cart
    let cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);

    // Clear existing items
    cart.items = [];
    cart.appliedOffers = [];

    // Add items from frontend
    for (const item of items) {
      const dish = await Dish.findById(item._id || item.dishId).populate(
        "category",
        "name"
      );
      if (dish && dish.isAvailable) {
        await cart.addItem(
          {
            ...dish.toObject(),
            categoryName: dish.category?.name,
          },
          item.quantity
        );
      }
    }

    // Auto-apply offers
    await autoApplyOffers(cart);

    // Populate and return updated cart
    await cart.populate([
      {
        path: "items.dishId",
        select: "name price image description isAvailable category",
        populate: {
          path: "category",
          select: "name",
        },
      },
      {
        path: "appliedOffers.offerId",
        select: "name offerType discountDetails",
      },
    ]);

    res.json({
      success: true,
      message: "Cart synced successfully",
      data: cart,
    });
  } catch (error) {
    console.error("Error syncing cart:", error);
    res.status(500).json({
      success: false,
      message: "Failed to sync cart",
      error: error.message,
    });
  }
};

// Helper function to auto-apply offers
const autoApplyOffers = async (cart) => {
  try {
    // Prepare order data for offer validation
    const orderData = {
      outletId: cart.outletId,
      orderAmount: cart.subtotal,
      customerId: cart.userId,
      items: cart.items.map((item) => ({
        dishId: item.dishId,
        dishName: item.dishName,
        quantity: item.quantity,
        price: item.price,
        category: item.category,
      })),
      foodChainId: cart.foodChainId,
    };

    // Get applicable offers and apply them
    const offerResult = await validateAndApplyOffers(orderData, cart.userId);

    if (offerResult.appliedOffers && offerResult.appliedOffers.length > 0) {
      // Clear existing offers
      cart.appliedOffers = [];

      // Apply new offers
      for (const appliedOffer of offerResult.appliedOffers) {
        await cart.applyOffer(
          appliedOffer.offer,
          appliedOffer.discountAmount,
          appliedOffer.applicableItems || []
        );
      }
    }
  } catch (error) {
    console.error("Error auto-applying offers:", error);
    // Don't throw error, just log it
  }
};
