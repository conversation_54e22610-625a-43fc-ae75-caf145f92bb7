import mongoose from "mongoose";

const cartItemSchema = new mongoose.Schema({
  dishId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Dish",
    required: true,
  },
  dishName: {
    type: String,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
    default: 1,
  },
  price: {
    type: Number,
    required: true,
    min: 0,
  },
  totalPrice: {
    type: Number,
    required: true,
    min: 0,
  },
  // Additional dish details for quick access
  dishImage: {
    type: String,
  },
  dishDescription: {
    type: String,
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Category",
  },
  categoryName: {
    type: String,
  },
  // Customizations or special instructions
  customizations: {
    type: String,
  },
  // Nutritional info if needed
  calories: {
    type: Number,
  },
  // Availability status
  isAvailable: {
    type: Boolean,
    default: true,
  },
  addedAt: {
    type: Date,
    default: Date.now,
  },
});

const appliedOfferSchema = new mongoose.Schema({
  offerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Offer",
    required: true,
  },
  offerName: {
    type: String,
    required: true,
  },
  offerType: {
    type: String,
    required: true,
  },
  discountAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  discountType: {
    type: String,
    enum: ["percentage", "fixed"],
    required: true,
  },
  // Items this offer was applied to
  applicableItems: [
    {
      dishId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Dish",
      },
      quantity: {
        type: Number,
        min: 1,
      },
    },
  ],
  appliedAt: {
    type: Date,
    default: Date.now,
  },
});

const cartSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  sessionId: {
    type: String,
    index: true,
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
    index: true,
  },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
    required: true,
    index: true,
  },
  items: [cartItemSchema],
  appliedOffers: [appliedOfferSchema],
  appliedCoupons: [
    {
      couponId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Coupon",
      },
      couponCode: {
        type: String,
      },
      discountAmount: {
        type: Number,
        min: 0,
      },
      appliedAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  // Cart totals
  subtotal: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalOfferDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalCouponDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  finalTotal: {
    type: Number,
    default: 0,
    min: 0,
  },
  // Tax calculations
  taxAmount: {
    type: Number,
    default: 0,
    min: 0,
  },
  taxPercentage: {
    type: Number,
    default: 0,
    min: 0,
  },
  // Delivery information
  deliveryFee: {
    type: Number,
    default: 0,
    min: 0,
  },
  packagingFee: {
    type: Number,
    default: 0,
    min: 0,
  },
  // Cart status
  status: {
    type: String,
    enum: ["active", "abandoned", "converted", "expired"],
    default: "active",
    index: true,
  },
  // Metadata
  deviceInfo: {
    userAgent: String,
    platform: String,
    ipAddress: String,
  },
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    index: true,
  },
});

// Create compound indexes for efficient queries
cartSchema.index({ userId: 1, foodChainId: 1, outletId: 1 });
cartSchema.index({ userId: 1, status: 1 });
cartSchema.index({ foodChainId: 1, status: 1 });
cartSchema.index({ createdAt: -1 });
cartSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Pre-save middleware to update timestamps and calculate totals
cartSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  this.lastAccessedAt = new Date();
  
  // Calculate subtotal
  this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  
  // Calculate total discounts
  this.totalOfferDiscount = this.appliedOffers.reduce(
    (sum, offer) => sum + offer.discountAmount,
    0
  );
  this.totalCouponDiscount = this.appliedCoupons.reduce(
    (sum, coupon) => sum + coupon.discountAmount,
    0
  );
  this.totalDiscount = this.totalOfferDiscount + this.totalCouponDiscount;
  
  // Calculate final total
  this.finalTotal = Math.max(
    0,
    this.subtotal - this.totalDiscount + this.taxAmount + this.deliveryFee + this.packagingFee
  );
  
  next();
});

// Instance methods
cartSchema.methods.addItem = function (dishData, quantity = 1) {
  const existingItemIndex = this.items.findIndex(
    (item) => item.dishId.toString() === dishData._id.toString()
  );
  
  if (existingItemIndex > -1) {
    // Update existing item
    this.items[existingItemIndex].quantity += quantity;
    this.items[existingItemIndex].totalPrice =
      this.items[existingItemIndex].quantity * this.items[existingItemIndex].price;
  } else {
    // Add new item
    this.items.push({
      dishId: dishData._id,
      dishName: dishData.name,
      quantity,
      price: dishData.price,
      totalPrice: dishData.price * quantity,
      dishImage: dishData.image,
      dishDescription: dishData.description,
      category: dishData.category,
      categoryName: dishData.categoryName,
      calories: dishData.calories,
      isAvailable: dishData.isAvailable,
    });
  }
  
  return this.save();
};

cartSchema.methods.removeItem = function (dishId) {
  this.items = this.items.filter(
    (item) => item.dishId.toString() !== dishId.toString()
  );
  return this.save();
};

cartSchema.methods.updateItemQuantity = function (dishId, quantity) {
  const itemIndex = this.items.findIndex(
    (item) => item.dishId.toString() === dishId.toString()
  );
  
  if (itemIndex > -1) {
    if (quantity <= 0) {
      this.items.splice(itemIndex, 1);
    } else {
      this.items[itemIndex].quantity = quantity;
      this.items[itemIndex].totalPrice =
        this.items[itemIndex].price * quantity;
    }
  }
  
  return this.save();
};

cartSchema.methods.clearCart = function () {
  this.items = [];
  this.appliedOffers = [];
  this.appliedCoupons = [];
  return this.save();
};

cartSchema.methods.applyOffer = function (offerData, discountAmount, applicableItems = []) {
  // Remove existing offer if it exists
  this.appliedOffers = this.appliedOffers.filter(
    (offer) => offer.offerId.toString() !== offerData._id.toString()
  );
  
  // Add new offer
  this.appliedOffers.push({
    offerId: offerData._id,
    offerName: offerData.name,
    offerType: offerData.offerType,
    discountAmount,
    discountType: offerData.discountDetails.discountType,
    applicableItems,
  });
  
  return this.save();
};

cartSchema.methods.removeOffer = function (offerId) {
  this.appliedOffers = this.appliedOffers.filter(
    (offer) => offer.offerId.toString() !== offerId.toString()
  );
  return this.save();
};

// Static methods
cartSchema.statics.findActiveCart = function (userId, foodChainId, outletId) {
  return this.findOne({
    userId,
    foodChainId,
    outletId,
    status: "active",
  });
};

cartSchema.statics.createOrUpdateCart = async function (userId, foodChainId, outletId, sessionId = null) {
  let cart = await this.findActiveCart(userId, foodChainId, outletId);
  
  if (!cart) {
    cart = new this({
      userId,
      foodChainId,
      outletId,
      sessionId,
      items: [],
      appliedOffers: [],
      appliedCoupons: [],
    });
    await cart.save();
  } else {
    cart.lastAccessedAt = new Date();
    if (sessionId) cart.sessionId = sessionId;
    await cart.save();
  }
  
  return cart;
};

export default mongoose.model("Cart", cartSchema);
